"use client";
import React from "react";
import { Modal } from "antd";
import { useTranslations, useLocale } from "next-intl";
import { ProductListItemFragment } from "@/gql/graphql";
import { filterCateImg } from "../Product/product-card";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { defaultLocale } from "@/config";
import { useProductStore } from "@/lib/store/product.store";
import useIsMobile from "@/lib/hooks/useIsMobile";
import { Link } from "@/navigation";
import { getColorImagePath } from "@/utils/categoryMapping";
interface CompareModalProps {
  open: boolean;
  onClose: () => void;
  compareList: any[];
  onRemoveProduct: (id: string) => void;
  onCloseDrawer?: () => void; // 新增：关闭比较抽屉的回调
}

const CompareModal: React.FC<CompareModalProps> = ({
  open,
  onClose,
  compareList,
  onRemoveProduct,
  onCloseDrawer,
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const { currencyUnit } = useProductStore();
  const isMobile = useIsMobile();
  console.log("compareList----",compareList);

  // 获取颜色值的辅助函数
  const getColorValue = (colorName: string) => {
    const colorMap: { [key: string]: string } = {
      'red': '#FF0000',
      'blue': '#0000FF',
      'green': '#008000',
      'yellow': '#FFFF00',
      'black': '#000000',
      'white': '#FFFFFF',
      'gray': '#808080',
      'grey': '#808080',
      'orange': '#FFA500',
      'purple': '#800080',
      'pink': '#FFC0CB',
      'brown': '#A52A2A'
    };
    return colorMap[colorName.toLowerCase()] || '#CCCCCC';
  };

  // 渲染价格
  const renderPrice = (price: any) => {
    if (!price?.start || !price?.stop) return "N/A";

    try {
      const startPrice = price.start.gross;
      const stopPrice = price.stop.gross;

      const isSamePrice = Number(startPrice.amount) === Number(stopPrice.amount);

      const formatPrice = (amount: any) => {
        return typeof amount === 'number'
          ? amount.toFixed(2)
          : typeof amount === 'string'
            ? parseFloat(amount).toFixed(2)
            : '0.00';
      };

      if (isSamePrice) {
        return `$ ${formatPrice(startPrice.amount)}`;
      } else {
        return `$ ${formatPrice(startPrice.amount)} - ${formatPrice(stopPrice.amount)}`;
      }
    } catch (error) {
      return "N/A";
    }
  };

  // 获取产品属性
  const getProductAttributes = (product: ProductListItemFragment) => {
    const attributes = product?.attributes?.map((item) => ({
      label: item.attribute?.translation?.name || item.attribute.name,
      value: item.values.map((i) => i.translation?.name || i.name).join(", "),
    })) || [];

    const variants = product?.variants?.map((variant) => ({
      name: variant?.translation?.name || variant?.name,
      sku: variant?.sku,
      weight: variant?.weight ? `${variant.weight.value} ${variant.weight.unit}` : null,
    })) || [];

    return { attributes, variants };
  };

  // 获取所有属性的 slug 列表（按显示顺序）
  const getAllAttributeSlugs = () => {
    return [
      "color",
      "sku", // 特殊处理
      "price", // 特殊处理
      "surface-material",
      "core-technology",
      "finishing",
      "printing",
      "edge-guard",
      "thickness",
      "weights",
      "length-inch",
      "width-inch",
      "grip-length-inch",
      "performance-type",
      "power-rating",
      "control-rating",
      "spin-rating"
    ];
  };

  // 根据 slug 获取翻译后的标签名称
  const getTranslatedLabel = (slug: string) => {
    const slugToTranslationKey: { [key: string]: string } = {
      "color": "common.Color",
      "sku": "SKU",
      "price": "common.Price (USD)",
      "surface-material": "common.Surface Material",
      "core-technology": "common.Core Material",
      "finishing": "common.Finishing",
      "printing": "common.Printing",
      "edge-guard": "common.Edge Guard",
      "thickness": "common.Thickness",
      "weights": "common.Weight",
      "length-inch": "common.Length",
      "width-inch": "common.Width",
      "grip-length-inch": "common.Grip Length",
      "performance-type": "common.Performance Type",
      "power-rating": "common.Power Rating",
      "control-rating": "common.Control Rating",
      "spin-rating": "common.Spin Rating"
    };

    const translationKey = slugToTranslationKey[slug];
    return translationKey?.startsWith("common.") ? t(translationKey) : translationKey || slug;
  };

  // 获取产品的特定属性值 - 基于 slug
  const getAttributeValue = (product: ProductListItemFragment, slug: string) => {
    // 特殊处理价格
    if (slug === "price") {
      return renderPrice(product.pricing?.priceRange);
    }

    // 特殊处理 SKU
    if (slug === "sku") {
      return product.variants?.[0]?.sku || "-";
    }

    // 特殊处理 Color 属性 - 从变体的 attributes 中获取
    if (slug === "color") {
      // 遍历所有变体，查找 Color 属性
      for (const variant of product.variants || []) {
        for (const attr of variant.attributes || []) {
          // 检查属性名称或 slug（如果存在）
          if (attr.attribute?.name === "Color" ||
              attr.attribute?.translation?.name === "Color" ||
              (attr.attribute as any)?.slug === "color") {
            // 获取颜色值
            const colorValue = attr.values?.[0]?.translation?.name || attr.values?.[0]?.name;
            if (colorValue) {
              return colorValue;
            }
          }
        }
      }
      // 如果在变体中找不到，尝试从产品级别的 attributes 中查找
      const colorAttribute = product?.attributes?.find(attr =>
        attr.attribute?.slug === "color"
      );
      if (colorAttribute) {
        const values = colorAttribute.values?.map(value =>
          value.translation?.name || value.name
        ).join(", ");
        return values || "-";
      }
      return "-";
    }

    // 根据 slug 查找对应的属性
    const matchedAttribute = product?.attributes?.find(attr =>
      attr.attribute?.slug === slug
    );

    // 添加调试信息
    if (slug === "weights") {
      console.log("Weight attribute debug:", {
        slug,
        productAttributes: product?.attributes?.map(attr => ({
          name: attr.attribute?.name,
          slug: attr.attribute?.slug,
          values: attr.values?.map(v => v.translation?.name || v.name)
        })),
        matchedAttribute: matchedAttribute ? {
          name: matchedAttribute.attribute?.name,
          slug: matchedAttribute.attribute?.slug,
          values: matchedAttribute.values?.map(v => v.translation?.name || v.name)
        } : null
      });
    }

    if (matchedAttribute) {
      // 获取属性值，优先使用翻译
      const values = matchedAttribute.values?.map(value =>
        value.translation?.name || value.name
      ).join(", ");
      return values || "-";
    }

    return "-";
  };

  // 渲染特殊属性值（如评分）
  const renderAttributeValue = (product: ProductListItemFragment, label: string, value: string) => {
    // 如果是评分相关的属性，渲染进度条样式
    if (label.toLowerCase().includes('rating') || label.toLowerCase().includes('power') || label.toLowerCase().includes('control') || label.toLowerCase().includes('spin')) {
      // 尝试解析数值并渲染进度条
      const numValue = parseFloat(value);
      if (!isNaN(numValue) && numValue >= 0) {
        // 根据不同的评分类型设置最大值
        let maxValue = 10; // 默认最大值为10

        // 可以根据具体的评分类型调整最大值
        if (label.toLowerCase().includes('power')) {
          maxValue = 10; // Power Rating 最大值为10
        } else if (label.toLowerCase().includes('control')) {
          maxValue = 10; // Control Rating 最大值为10
        } else if (label.toLowerCase().includes('spin')) {
          maxValue = 10; // Spin Rating 最大值为10
        }

        // 确保数值不超过最大值
        const clampedValue = Math.min(numValue, maxValue);
        const percentage = (clampedValue / maxValue) * 100;

        return (
          <div className="flex justify-center w-full">
            <div className={`${isMobile ? 'w-[80px]' : 'w-[120px]'}`}>
              <div className={`flex items-center bg-white border-black border rounded ${isMobile ? 'h-3' : 'h-4'} overflow-hidden`}>
                <div
                  className="bg-black h-full transition-all duration-300 rounded"
                  style={{ width: `${percentage}%` }}
                ></div>
                <div
                  className="bg-white h-full flex-1"
                ></div>
              </div>
            </div>
          </div>
        );
      }
    }

    // 特殊处理颜色属性
    if (label.toLowerCase() === 'color') {
      // 如果值是横杠，直接返回横杠
      if (value === '-') {
        return <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black text-center`}>-</span>;
      }

      // 尝试获取颜色对应的图片路径
      const colorImagePath = getColorImagePath(value, product?.category?.slug);

      // 如果有对应的图片，显示图片
      if (colorImagePath) {
        return (
          <div className="flex items-center justify-center">
            <div className={`${isMobile ? 'w-[54px] h-[32px]' : 'w-[74px] h-[42px]'}  border border-gray-300 overflow-hidden`}>
              <SEOOptimizedImage
                src={colorImagePath}
                alt={`RC ${value} color`}
                width={40}
                height={40}
                unoptimized
                quality={100}
                priority
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        );
      }

      // 检查是否是16进制颜色值
      const isHexColor = /^#[0-9A-Fa-f]{6}$/.test(value);

      if (isHexColor) {
        return (
          <div className="flex items-center justify-center">
            <div
              className="w-14 h-5"
              style={{ backgroundColor: value }}
            ></div>
          </div>
        );
      } else {
        // 如果不是16进制颜色，尝试将颜色名称转换为颜色
        const colorMap: { [key: string]: string } = {
          'red': '#FF0000',
          'blue': '#0000FF',
          'green': '#008000',
          'yellow': '#FFFF00',
          'black': '#000000',
          'white': '#FFFFFF',
          'gray': '#808080',
          'grey': '#808080',
          'orange': '#FFA500',
          'purple': '#800080',
          'pink': '#FFC0CB',
          'brown': '#A52A2A'
        };

        const colorValue = colorMap[value.toLowerCase()];
        if (colorValue) {
          return (
            <div className="flex items-center justify-center">
              <div
                className="w-14 h-5"
                style={{ backgroundColor: colorValue }}
              ></div>
            </div>
          );
        } else {
          // 如果无法匹配颜色，显示横杠
          return <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black text-center`}>-</span>;
        }
      }
    }

    return <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black text-center`}>{value}</span>;
  };

  const attributeSlugs = getAllAttributeSlugs();

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      width={isMobile ? "100%" : "90%"}
      style={{
        maxWidth: isMobile ? "100%" : "900px",
        margin: isMobile ? 0 : "auto",
        top: isMobile ? 0 : 20
      }}
      className="compare-modal"
      title={
        <div className="flex items-center justify-center relative">
          <h3 className={`${isMobile ? 'text-lg' : 'text-[34px]'} mb-8 font-semibold text-center`}>Product Comparison</h3>
          <button
            onClick={onClose}
            className={`absolute right-0 top-0 text-black hover:text-gray-700 ${isMobile ? 'text-xl' : 'text-2xl'}`}
          >
            ×
          </button>
        </div>
      }
      closable={false}
    >
      <div className={`${isMobile ? 'max-h-[90vh]' : 'max-h-[80vh]'}`}>
        {/* 使用单一表格结构，头部固定 */}
        <div className="border-r border-b border-gray-200">
          <div className={`overflow-y-auto ${isMobile ? 'max-h-[80vh]' : 'max-h-[70vh]'}`}>
            <table className="w-full table-fixed">
              <colgroup>
                <col className={`${isMobile ? 'w-24' : 'w-40 md:w-48'}`} />
                <col className="w-1/2" />
                <col className="w-1/2" />
              </colgroup>

              {/* 固定的产品信息头部 */}
              <thead className="sticky top-0 z-10">
                <tr className="bg-white border-b border-gray-200">
                  {/* 空白单元格 */}
                  <th className="border-r border-gray-200 pt-4"></th>

                  {/* 产品单元格 */}
                  {[0, 1].map((index) => {
                    const item = compareList[index];

                    if (!item?.node) {
                      return (
                        <th key={`empty-${index}`} className={`border-gray-200 ${isMobile ? 'p-2 px-3' : 'p-4'} pt-4 bg-white ${index === 0 ? 'border-r' : 'border-r'}`}>
                          <div className={`flex flex-col items-center justify-center ${isMobile ? 'py-2' : 'py-4'}`}>
                            {/* 空产品占位符 */}
                            <div className={`${isMobile ? 'w-12 h-12' : 'w-16 h-16 md:w-20 md:h-20'} rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center mb-3`}>
                              {/* <i className="ri-add-line text-xl text-gray-400"></i> */}
                            </div>
                            <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium text-black text-center`}>Add Product</span>
                          </div>
                        </th>
                      );
                    }

                    const product = item.node as ProductListItemFragment;
                    const imgs = filterCateImg(product.metadata);
                    const productName = locale === defaultLocale
                      ? product.name
                      : product.translation?.name || product.name;

                    return (
                      <th key={product.id} className={`border-gray-200 ${isMobile ? 'p-2 px-3' : 'p-4 px-10'} pt-4 relative bg-white ${index === 0 ? 'border-r' : 'border-r'} overflow-visible`}>
                        <div className="flex flex-col items-center justify-center">
                          {/* 产品图片 */}
                          <div className={`w-full ${isMobile ? 'h-[100px]' : 'h-[220px]'} rounded-lg border border-[#e5e5e5] mb-3 relative`}>
                            <SEOOptimizedImage
                              src={imgs[0]?.url || "/image/default-image.webp"}
                              alt={productName}
                              width={200}
                              height={200}
                              className="w-full h-full object-cover rounded-lg"
                            />
                            {/* 删除按钮 */}
                            <button
                              onClick={() => onRemoveProduct(product.id)}
                              className={`absolute -top-2 -right-2 flex ${isMobile ? 'h-6 w-6' : 'h-7 w-7'} items-center justify-center rounded-full bg-white border text-black shadow-lg transition-all duration-200 z-20`}
                            >
                              <i className={`ri-close-line ${isMobile ? 'text-sm' : 'text-lg'} !font-normal`}></i>
                            </button>
                          </div>

                          {/* 产品名称 */}
                          <h3 className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-medium text-black text-center line-clamp-2 mb-3 px-1 leading-tight`}>
                            {productName}
                          </h3>

                          {/* View Details 按钮 */}
                          <Link
                            href={`/product/${product.slug}`}
                            className={`${isMobile ? 'px-3 py-2 text-[10px]' : 'px-5 py-3 text-xs'} bg-[#83c000] text-white hover:text-white rounded-full !font-normal hover:bg-opacity-80 transition-colors`}
                            onClick={() => {
                              // 关闭比较模态框
                              onClose();
                              // 关闭比较抽屉
                              onCloseDrawer?.();
                            }}
                          >
                            {t("order.View Details")}
                          </Link>
                        </div>
                      </th>
                    );
                  })}
                </tr>
              </thead>

              {/* 属性对比表格主体 */}
              <tbody>
                {attributeSlugs.map((slug, index) => {
                  const translatedLabel = getTranslatedLabel(slug);
                  return (
                    <tr
                      key={slug}
                      className={`${index % 2 === 0 ? "bg-white" : "bg-white"
                        } border-b border-[#e5e5e5] last:border-b-0`}
                    >
                      {/* 属性标签单元格 */}
                      <td className={`border-r border-[#e5e5e5] ${isMobile ? 'px-2 py-2' : 'px-4 py-3'}`}>
                        <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black`}>
                          {translatedLabel}
                        </span>
                      </td>

                      {/* 产品属性值单元格 */}
                      {[0, 1].map((productIndex) => {
                        const item = compareList[productIndex];

                        if (!item?.node) {
                          return (
                            <td key={`empty-${slug}-${productIndex}`} className={`border-[#e5e5e5] text-black ${isMobile ? 'px-2 py-2' : 'px-4 py-3'} text-center ${productIndex === 0 ? 'border-r' : 'border-r'}`}>
                              <span className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-black`}>-</span>
                            </td>
                          );
                        }

                        const product = item.node as ProductListItemFragment;
                        const value = getAttributeValue(product, slug);

                        return (
                          <td key={`${product.id}-${slug}`} className={`border-[#e5e5e5] ${isMobile ? 'px-2 py-2' : 'px-4 py-3'} text-center ${productIndex === 0 ? 'border-r' : 'border-r'}`}>
                            {renderAttributeValue(product, translatedLabel, value)}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CompareModal;
