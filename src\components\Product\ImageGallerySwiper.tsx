"use client";

import React, { useEffect, useMemo, useState, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/swiper-bundle.css";
import Image from "next/image";
import { Button, Empty, Skeleton } from "antd";
import { useTranslations, useLocale } from "next-intl";
import axios from "axios";
import LoadNormalModal from "../3DNormalModal";
import { defaultLocale } from "@/config";
import { productTranslationName } from "@/lib/utils/util";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css"; // 需要引入样式
// 常量
const spaceBetween = 10;
const slidesPerView = 5;
const mobileBreakpoint = 768;

// 测试数据 - 10张图片用于测试轮播效果
const testImages = [
	{
		id: "test1",
		name: "测试图片1",
		alt: "测试图片1",
		type: "image/webp",
		url: "https://picsum.photos/id/1/600/600"
	},
	{
		id: "test2",
		name: "测试图片2",
		alt: "测试图片2",
		type: "image/webp",
		url: "https://picsum.photos/id/20/600/600"
	},
	{
		id: "test3",
		name: "测试图片3",
		alt: "测试图片3",
		type: "image/webp",
		url: "https://picsum.photos/id/30/600/600"
	},
	{
		id: "test4",
		name: "测试图片4",
		alt: "测试图片4",
		type: "image/webp",
		url: "https://picsum.photos/id/40/600/600"
	},
	{
		id: "test5",
		name: "测试图片5",
		alt: "测试图片5",
		type: "image/webp",
		url: "https://picsum.photos/id/50/600/600"
	},
	{
		id: "test6",
		name: "测试图片6",
		alt: "测试图片6",
		type: "image/webp",
		url: "https://picsum.photos/id/60/600/600"
	},
	{
		id: "test7",
		name: "测试图片7",
		alt: "测试图片7",
		type: "image/webp",
		url: "https://picsum.photos/id/70/600/600"
	},
	{
		id: "test8",
		name: "测试图片8",
		alt: "测试图片8",
		type: "image/webp",
		url: "https://picsum.photos/id/80/600/600"
	},
	{
		id: "test9",
		name: "测试图片9",
		alt: "测试图片9",
		type: "image/webp",
		url: "https://picsum.photos/id/90/600/600"
	},
	{
		id: "test10",
		name: "测试图片10",
		alt: "测试图片10",
		type: "image/webp",
		url: "https://picsum.photos/id/100/600/600"
	}
];

interface ProductMedia {
	id: string;
	name: string;
	alt: string;
	type: string;
	url: string;
}

//默认数据
const defaultImage: ProductMedia[] = [{
	id: "default",
	name: "default-product.webp",
	alt: "default product image",
	type: "image/webp",
	url: "/image/default-image.webp"
}];

const getImagesForProduct = (product: any): ProductMedia[] => {
	if (!product?.metadata) return defaultImage;
	try {
		const media = JSON.parse(product.media);
		// @ts-ignore
		return Array.isArray(media) && media.length > 0 ? media : defaultImage;
	} catch (error) {
		console.error('Error parsing product media:', error);
		return defaultImage;
	}
};

interface ImageGalleryProps {
	product: any;
	useTestData?: boolean; // 是否使用测试数据
}

function ImageGallery({ product, useTestData = false }: ImageGalleryProps) {
	const locale = useLocale();
	const videoRef = useRef<HTMLVideoElement>(null);
	const mainImgRef = useRef<HTMLDivElement>(null);
	const thumbsContainerRef = useRef<HTMLDivElement>(null);
	const [active, setActive] = useState(0);
	const [mainImage, setMainImage] = useState<string>(defaultImage[0].url);
	const [videoUrl, setVideoUrl] = useState<string>("");
	const [isPlaying, setIsPlaying] = useState(false);
	const [images, setImages] = useState<ProductMedia[]>(useTestData ? testImages : defaultImage);
	const [swiperInstance, setSwiperInstance] = useState<any>(null);
	const [thumbnailSwiper, setThumbnailSwiper] = useState<any>(null);
	const [displayType, setDisplayType] = useState<"img" | "mp4" | "3d">("img");
	const t = useTranslations();
	const [isMobile, setIsMobile] = useState(false);
	const [isTablet, setIsTablet] = useState(false);
	const [thumbsOrientation, setThumbsOrientation] = useState<"vertical" | "horizontal">("vertical");
	const [mainImageHeight, setMainImageHeight] = useState(600);
	// Lightbox状态
	const [isLightboxOpen, setIsLightboxOpen] = useState(false);
	const [lightboxIndex, setLightboxIndex] = useState(0);
	// 图片放大功能状态
	const [isZoomed, setIsZoomed] = useState(false);
	const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });
	const zoomContainerRef = useRef<HTMLDivElement>(null);
	// 监听窗口大小变化
	useEffect(() => {
		const handleResize = () => {
			const width = window.innerWidth;
			setIsMobile(width < mobileBreakpoint);
			setIsTablet(width >= mobileBreakpoint && width < 1024);
			setThumbsOrientation(width < mobileBreakpoint ? "horizontal" : "vertical");

			// 更新主图容器高度
			if (mainImgRef.current) {
				const height = mainImgRef.current.offsetHeight;
				setMainImageHeight(height);
			}
		};

		handleResize(); // 初始调用
		window.addEventListener('resize', handleResize);
		return () => window.removeEventListener('resize', handleResize);
	}, []);

	// 初始化图片列表
	const changeProductImages = (product: any) => {
		if (useTestData) {
			setImages(testImages);
			setMainImage(testImages[0].url);
			return;
		}

		const imagesFromProduct = getImagesForProduct(product);
		setImages(imagesFromProduct);
		setMainImage(imagesFromProduct[0]?.url || defaultImage[0].url);
	};

	// 在主图渲染后更新高度
	// useEffect(() => {
	// 	if (mainImgRef.current) {
	// 		const resizeObserver = new ResizeObserver(entries => {
	// 			for (let entry of entries) {
	// 				const height = entry.contentRect.height;
	// 				setMainImageHeight(height);
	// 			}
	// 		});

	// 		resizeObserver.observe(mainImgRef.current);
	// 		return () => {
	// 			if (mainImgRef.current) {
	// 				resizeObserver.unobserve(mainImgRef.current);
	// 			}
	// 		};
	// 	}
	// }, [mainImgRef.current]);

	useEffect(() => {
		if (product || useTestData) {
			setActive(0);
			if (!useTestData) fetchVideoUrl();
			changeProductImages(product);
		}
	}, [product, useTestData]);

	// 获取视频URL
	async function fetchVideoUrl() {
		if (useTestData) return;

		try {
			if (!product?.metadata) return;

			const coverMp4Item = product.metadata.find((item) => item.key === "cover_mp4");
			if (!coverMp4Item) return;

			const coverMp4Obj = JSON.parse(coverMp4Item.value || "[]")[0];
			if (!coverMp4Obj) return;

			const { data } = await axios(
				`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/medias/video/${coverMp4Obj.id}`
			);

			setVideoUrl(data.detail.video_url);
		} catch (error) {
			console.error("Error fetching video URL:", error);
		}
	}

	const handlePrevOrNext = (type: "prev" | "next") => {
		if (type === "prev" && active > 0) {
			setActive(active - 1);
			setMainImage(images[active - 1]?.url || defaultImage[0].url);
			thumbnailSwiper?.slideTo(active - 1);
		} else if (type === "next" && active < images.length - 1) {
			setActive(active + 1);
			setMainImage(images[active + 1]?.url || defaultImage[0].url);
			thumbnailSwiper?.slideTo(active + 1);
		}
	};

	const threeModel = useMemo<any>(() => {
		if (useTestData) return null;
		if (!product?.metadata) return null;

		const json = product.metadata.find((item) => item.key === "threeModel");
		return json ? JSON.parse(json.value)[0] : null;
	}, [product, useTestData]);

	const toggleVideo = () => {
		if (!videoRef.current) return;

		setIsPlaying(!isPlaying);
		setDisplayType('mp4');

		if (isPlaying) {
			videoRef.current.pause();
		} else {
			videoRef.current.play();
		}
	};

	// 处理鼠标移动事件，计算放大镜位置
	const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
		if (!zoomContainerRef.current) return;

		const rect = zoomContainerRef.current.getBoundingClientRect();
		const x = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
		const y = Math.max(0, Math.min(100, ((e.clientY - rect.top) / rect.height) * 100));

		setZoomPosition({ x, y });

		// 如果还没有放大，则启用放大
		if (!isZoomed && displayType === 'img' && !isMobile) {
			setIsZoomed(true);
		}
	};

	// 鼠标进入时准备放大
	const handleMouseEnter = () => {
		// 不在这里直接设置放大，而是在鼠标移动时设置
	};

	// 鼠标离开时禁用放大
	const handleMouseLeave = () => {
		setIsZoomed(false);
		setZoomPosition({ x: 50, y: 50 }); // 重置到中心位置
	};

	return (
		<div className="product-gallery w-full select-none">
			<div className={`relative flex ${isMobile ? 'flex-col-reverse' : 'flex-row'} gap-3 md:gap-4 lg:gap-6`}>
				{/* 缩略图区域 */}
				<div
					ref={thumbsContainerRef}
					className={`
							${isMobile ? 'w-full h-20 mt-3' : 'w-24'}
							relative flex-shrink-0
						`}
					style={!isMobile ? { height: `${mainImageHeight}px` } : {}}
				>
					<Swiper
						onSwiper={setThumbnailSwiper}
						direction={thumbsOrientation}
						slidesPerView={thumbsOrientation === "horizontal" ? 4.5 : 'auto'}
						spaceBetween={8}
						className="h-full thumbs-swiper"
						slideToClickedSlide={true}
						watchSlidesProgress={true}
					>
						{images.map((image, index) => (
							<SwiperSlide key={index} className="h-auto">
								<div
									className={`
											cursor-pointer overflow-hidden rounded-lg border-2 transition-all duration-200
											${active === index ? 'border-black shadow-sm' : 'border-transparent opacity-70 hover:opacity-100'}
										`}
									onClick={() => {
										setActive(index);
										setMainImage(image.url);
										setDisplayType('img');
									}}
								>
									<div className={`
											relative aspect-square overflow-hidden 
											${isMobile ? 'w-full h-16' : 'w-full'}
										`}>
										<SEOOptimizedImage
											src={image.url}
											alt={image.alt || product?.name}
											fill
											className="object-contain w-full h-full"
											quality={100}
											priority
										/>
									</div>
								</div>

								{videoUrl && index === 0 && (
									<div
										onClick={(e) => {
											e.stopPropagation();
											toggleVideo();
										}}
										className="w-8 h-8 cursor-pointer rounded-full bg-black/60 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 flex items-center justify-center"
									>
										<i className={`${isPlaying ? 'ri-pause-line' : 'ri-play-line'} text-white`}></i>
									</div>
								)}
							</SwiperSlide>
						))}
					</Swiper>

					{!isMobile && images.length > 5 && (
						<div className="absolute left-1/2 -translate-x-1/2 bottom-2 z-10 flex gap-1">
							<button
								className="w-8 h-8 rounded-full bg-white shadow flex items-center justify-center hover:bg-gray-50"
								onClick={() => thumbnailSwiper?.slidePrev()}
							>
								<i className="ri-arrow-up-s-line"></i>
							</button>
							<button
								className="w-8 h-8 rounded-full bg-white shadow flex items-center justify-center hover:bg-gray-50"
								onClick={() => thumbnailSwiper?.slideNext()}
							>
								<i className="ri-arrow-down-s-line"></i>
							</button>
						</div>
					)}
				</div>

				{/* 主图区域 */}
				<div
					ref={mainImgRef}
					className="relative flex-grow aspect-square overflow-hidden group border-[1px] border-[#f7f7f7]"
				>
					{/* 图片显示 */}
					<div className={`absolute inset-0 transition-opacity duration-300 ${displayType === "img" ? "opacity-100 z-10" : "opacity-0 z-0"}`}>
						<div
							ref={zoomContainerRef}
							className="relative h-full w-full overflow-hidden"
							onMouseMove={handleMouseMove}
							onMouseEnter={handleMouseEnter}
							onMouseLeave={handleMouseLeave}
						>
							<SEOOptimizedImage
								src={mainImage}
								alt={locale === defaultLocale ? product?.name : productTranslationName(product?.translation?.name) || product?.name}
								fill
								priority
								className="object-contain w-full h-full transition-transform duration-200"
								style={isZoomed ? {
									transform: 'scale(2)',
									transformOrigin: `${zoomPosition.x}% ${zoomPosition.y}%`
								} : {
									transform: 'scale(1)'
								}}
							/>
						</div>
					</div>

					{/* 视频显示 */}
					{videoUrl && (
						<div className={`absolute inset-0 transition-opacity duration-300 ${displayType === "mp4" ? "opacity-100 z-10" : "opacity-0 z-0"}`}>
							<div className="relative h-full w-full bg-black">
								<video
									ref={videoRef}
									src={videoUrl}
									poster={mainImage}
									autoPlay={isPlaying}
									controls={true}
									muted={isMobile}
									playsInline
									loop
									className="w-full h-full object-contain"
								></video>

								{!isPlaying && (
									<div className="absolute inset-0 flex items-center justify-center cursor-pointer" onClick={toggleVideo}>
										<div className="w-16 h-16 rounded-full bg-black/50 flex items-center justify-center">
											<i className="ri-play-fill text-white text-3xl"></i>
										</div>
									</div>
								)}
							</div>
						</div>
					)}

					{/* 3D模型显示 */}
					{threeModel && (
						<div className={`absolute inset-0 transition-opacity duration-300 ${displayType === "3d" ? "opacity-100 z-10" : "opacity-0 z-0"}`}>
							<div className="h-full w-full bg-gray-100">
								<LoadNormalModal
									modalUrl={process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + `/medias/file_download/` + threeModel.id}
									className="w-full h-full"
									fileName={threeModel.name}
									metalness={threeModel.metalness}
									color={threeModel.color}
									roughness={threeModel.roughness}
								/>
							</div>
						</div>
					)}

					{/* 放大图片按钮 */}
					{displayType === "img" && (
						<button 
							className={`absolute bottom-3 right-3 z-20 w-10 h-10 rounded-full bg-white/80 hover:bg-black hover:text-white shadow flex items-center justify-center transition-colors duration-200`}
							// 注释原来的hover显示逻辑，以便将来切换: ${isMobile ? '' : 'opacity-0 group-hover:opacity-100'}
							onClick={() => {
								setLightboxIndex(active);
								setIsLightboxOpen(true);
							}}
						>
							<i className="ri-zoom-in-line text-lg"></i>
						</button>
					)}

					{/* 控制按钮 */}
					{/* <div className="absolute top-3 right-3 z-20 flex items-center gap-2">
						<div className="flex rounded-full bg-white/80 p-1 shadow-sm backdrop-blur-sm">
							<button 
								className={`w-8 h-8 rounded-full flex items-center justify-center ${displayType === "img" ? "bg-black text-white" : ""}`}
								onClick={() => setDisplayType("img")}
							>
								<i className="ri-image-line"></i>
							</button>
							
							{videoUrl && (
								<button 
									className={`w-8 h-8 rounded-full flex items-center justify-center ${displayType === "mp4" ? "bg-black text-white" : ""}`}
									onClick={() => {
										setDisplayType("mp4");
										setIsPlaying(true);
										videoRef.current?.play();
									}}
								>
									<i className="ri-video-line"></i>
								</button>
							)}
							
							{threeModel && (
								<button 
									className={`w-8 h-8 rounded-full flex items-center justify-center ${displayType === "3d" ? "bg-black text-white" : ""}`}
									onClick={() => setDisplayType("3d")}
								>
									<i className="ri-cube-line"></i>
								</button>
							)}
						</div>
					</div> */}

					{/* 左右导航按钮 */}
					{images.length > 1 && (
						<>
							<button
								className={`absolute left-2 top-1/2 -translate-y-1/2 z-20 w-8 h-8 rounded-full bg-white/80 hover:bg-black hover:text-white shadow flex items-center justify-center ${isMobile ? '' : 'opacity-0 group-hover:opacity-100'} transition-colors duration-200`}
								onClick={() => handlePrevOrNext("prev")}
								disabled={active === 0}
							>
								<i className="ri-arrow-left-s-line text-lg"></i>
							</button>
							<button
								className={`absolute right-2 top-1/2 -translate-y-1/2 z-20 w-8 h-8 rounded-full bg-white/80 hover:bg-black hover:text-white shadow flex items-center justify-center ${isMobile ? '' : 'opacity-0 group-hover:opacity-100'} transition-colors duration-200`}
								onClick={() => handlePrevOrNext("next")}
								disabled={active === images.length - 1}
							>
								<i className="ri-arrow-right-s-line text-lg"></i>
							</button>
						</>
					)}
				</div>
			</div>

			{/* 移动端缩略图指示器 */}
			{isMobile && images.length > 1 && (
				<div className="flex justify-center gap-1.5 mt-3">
					{images.map((_, index) => (
						<button
							key={index}
							className={`h-1.5 rounded-full transition-all ${active === index ? 'bg-black w-6' : 'bg-gray-300 w-1.5'}`}
							onClick={() => {
								setActive(index);
								setMainImage(images[index].url);
							}}
						/>
					))}
				</div>
			)}

			{/* Lightbox 组件 */}
			{isLightboxOpen && (
				<Lightbox
					mainSrc={images[lightboxIndex].url}
					nextSrc={images[(lightboxIndex + 1) % images.length].url}
					prevSrc={images[(lightboxIndex + images.length - 1) % images.length].url}
					onCloseRequest={() => setIsLightboxOpen(false)}
					onMovePrevRequest={() => 
						setLightboxIndex((lightboxIndex + images.length - 1) % images.length)
					}
					onMoveNextRequest={() => 
						setLightboxIndex((lightboxIndex + 1) % images.length)
					}
					imageTitle={locale === defaultLocale ? product?.name : productTranslationName(product?.translation?.name) || product?.name}
					imageCaption={images[lightboxIndex].alt || product?.name}
					reactModalStyle={{ overlay: { zIndex: 1500 } }}
				/>
			)}
		</div>
	);
}

// 使用测试数据的包装组件（方便在页面中快速测试）
export function TestImageGallery() {
	return <ImageGallery product={null} useTestData={true} />;
}

export default React.memo(ImageGallery);

<style jsx global>{`
  .thumbs-swiper .swiper-slide {
    opacity: 0.7;
    transition: opacity 0.3s;
  }

  .thumbs-swiper .swiper-slide-thumb-active {
    opacity: 1;
  }
`}</style>

