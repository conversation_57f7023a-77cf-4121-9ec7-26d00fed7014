/* eslint-disable */
import * as types from './graphql';



/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 */
const documents = {
    "query ChannelsList {\n  channels {\n    id\n    name\n    slug\n    isActive\n    currencyCode\n    countries {\n      country\n      code\n    }\n  }\n}": types.ChannelsListDocument,
    "mutation CheckoutAddLine($id: ID!, $productVariantId: ID!, $quantity: Int!, $locale: LanguageCodeEnum!) {\n  checkoutLinesAdd(\n    id: $id\n    lines: [{quantity: $quantity, variantId: $productVariantId}]\n  ) {\n    checkout {\n      ...CheckoutItem\n    }\n    errors {\n      message\n    }\n  }\n}": types.CheckoutAddLineDocument,
    "mutation checkoutAddPromoCode($checkoutId: ID!, $promoCode: String!) {\n  checkoutAddPromoCode(checkoutId: $checkoutId, promoCode: $promoCode) {\n    errors {\n      code\n      message\n    }\n    checkout {\n      id\n      giftCards {\n        displayCode\n        id\n        last4CodeChars\n      }\n    }\n  }\n}": types.CheckoutAddPromoCodeDocument,
    "mutation CheckoutCreate($channel: String!, $locale: LanguageCodeEnum!) {\n  checkoutCreate(input: {channel: $channel, lines: []}) {\n    checkout {\n      ...CheckoutItem\n    }\n  }\n}": types.CheckoutCreateDocument,
    "mutation CreateTransaction($checkoutId: ID!, $amount: PositiveDecimal!, $metadata: [MetadataInput!]) {\n  transactionCreate(\n    id: $checkoutId\n    transaction: {name: \"Payment transaction\", message: \"Checkout payment\", pspReference: \"paypal\", availableActions: [CHARGE], amountAuthorized: {amount: $amount, currency: \"USD\"}, amountCharged: {amount: 0, currency: \"USD\"}, amountRefunded: {amount: 0, currency: \"USD\"}, amountCanceled: {amount: 0, currency: \"USD\"}, metadata: $metadata}\n  ) {\n    transaction {\n      id\n      name\n      message\n      metadata {\n        key\n        value\n      }\n    }\n    errors {\n      field\n      message\n    }\n  }\n}": types.CreateTransactionDocument,
    "mutation checkoutRemovePromoCode($checkoutId: ID!, $promoCodeId: ID!) {\n  checkoutRemovePromoCode(checkoutId: $checkoutId, promoCodeId: $promoCodeId) {\n    errors {\n      code\n      message\n    }\n    checkout {\n      id\n      giftCards {\n        displayCode\n        id\n        last4CodeChars\n      }\n    }\n  }\n}": types.CheckoutRemovePromoCodeDocument,
    "mutation CheckoutLinesDelete($id: ID!, $linesIds: [ID!]!, $locale: LanguageCodeEnum!) {\n  checkoutLinesDelete(id: $id, linesIds: $linesIds) {\n    checkout {\n      ...CheckoutItem\n    }\n    errors {\n      message\n    }\n  }\n}": types.CheckoutLinesDeleteDocument,
    "mutation CheckoutDeliveryMethodUpdate($deliveryMethodId: ID!, $checkoutId: ID!) {\n  checkoutDeliveryMethodUpdate(\n    deliveryMethodId: $deliveryMethodId\n    id: $checkoutId\n  ) {\n    checkout {\n      id\n    }\n    errors {\n      code\n      field\n      message\n    }\n  }\n}": types.CheckoutDeliveryMethodUpdateDocument,
    "query CheckoutFind($id: ID!, $locale: LanguageCodeEnum!) {\n  checkout(id: $id) {\n    ...CheckoutItem\n  }\n}": types.CheckoutFindDocument,
    "fragment CheckoutItem on Checkout {\n  id\n  email\n  discount {\n    amount\n    currency\n  }\n  giftCards {\n    displayCode\n    id\n    last4CodeChars\n  }\n  billingAddress {\n    city\n    cityArea\n    companyName\n    countryArea\n    country {\n      code\n    }\n    id\n    lastName\n    phone\n    streetAddress1\n    streetAddress2\n  }\n  shippingAddress {\n    city\n    cityArea\n    companyName\n    countryArea\n    country {\n      code\n    }\n    firstName\n    id\n    lastName\n    phone\n    streetAddress1\n    streetAddress2\n  }\n  shippingMethod {\n    id\n    name\n    message\n  }\n  availableShippingMethods {\n    id\n  }\n  shippingMethods {\n    id\n  }\n  availableCollectionPoints {\n    id\n  }\n  lines {\n    id\n    quantity\n    totalPrice {\n      gross {\n        amount\n        currency\n      }\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n    }\n    variant {\n      ...ProductVariantItem\n      product {\n        media: metafield(key: \"media\")\n        name\n        slug\n        chargeTaxes\n      }\n    }\n  }\n  totalPrice {\n    gross {\n      amount\n      currency\n    }\n    net {\n      amount\n      currency\n    }\n    tax {\n      amount\n      currency\n    }\n  }\n}": types.CheckoutItemFragmentDoc,
    "mutation CheckoutLinesUpdate($id: ID!, $locale: LanguageCodeEnum!, $lines: [CheckoutLineUpdateInput!]!) {\n  checkoutLinesUpdate(id: $id, lines: $lines) {\n    checkout {\n      ...CheckoutItem\n    }\n    errors {\n      code\n      message\n      field\n    }\n  }\n}": types.CheckoutLinesUpdateDocument,
    "mutation CheckoutShippingAddressUpdate($checkoutId: ID!, $firstName: String!, $lastName: String!, $country: CountryCode!, $companyName: String, $countryArea: String, $streetAddress1: String!, $city: String, $phone: String!, $postalCode: String!) {\n  checkoutShippingAddressUpdate(\n    checkoutId: $checkoutId\n    shippingAddress: {firstName: $firstName, lastName: $lastName, country: $country, companyName: $companyName, countryArea: $countryArea, streetAddress1: $streetAddress1, phone: $phone, postalCode: $postalCode, city: $city}\n  ) {\n    errors {\n      code\n      field\n      message\n    }\n    checkout {\n      id\n      shippingAddress {\n        firstName\n        lastName\n        country {\n          code\n        }\n        companyName\n        countryArea\n        streetAddress1\n        city\n        phone\n        postalCode\n      }\n    }\n  }\n}": types.CheckoutShippingAddressUpdateDocument,
    "query CollectionList($first: Int!, $channel: String!, $locale: LanguageCodeEnum!) {\n  collections(first: $first, channel: $channel) {\n    edges {\n      node {\n        id\n        name\n        slug\n        description\n        metadata {\n          key\n          value\n        }\n        translation(languageCode: $locale) {\n          name\n          description\n        }\n      }\n    }\n  }\n}": types.CollectionListDocument,
    "query CurrentUser {\n  me {\n    ...UserDetails\n  }\n}": types.CurrentUserDocument,
    "query CurrentUserOrderList {\n  me {\n    ...UserDetails\n    orders(first: 10) {\n      edges {\n        node {\n          ...OrderDetails\n        }\n      }\n    }\n  }\n}": types.CurrentUserOrderListDocument,
    "query FindCheckoutId($channel: String!) {\n  me {\n    checkouts(first: 1, last: 1, channel: $channel) {\n      edges {\n        node {\n          id\n          chargeStatus\n        }\n      }\n    }\n  }\n}": types.FindCheckoutIdDocument,
    "query GetShippingMethods($checkoutId: ID!) {\n  checkout(id: $checkoutId) {\n    availableShippingMethods {\n      id\n      name\n      price {\n        amount\n        currency\n      }\n    }\n  }\n}": types.GetShippingMethodsDocument,
    "query GetUserAddresses {\n  me {\n    addresses {\n      id\n      city\n      lastName\n      companyName\n      country {\n        country\n        code\n        vat {\n          countryCode\n        }\n      }\n      firstName\n      countryArea\n      streetAddress1\n      phone\n      postalCode\n      isDefaultShippingAddress\n      isDefaultBillingAddress\n    }\n  }\n}": types.GetUserAddressesDocument,
    "fragment CategoryLocaleItem on Category {\n  translation(languageCode: $locale) {\n    name\n    description\n    seoDescription\n    seoTitle\n  }\n}\n\nfragment ProductLocaleItem on Product {\n  translation(languageCode: $locale) {\n    name\n  }\n}\n\nfragment CollectionLocaleItem on Collection {\n  translation(languageCode: $locale) {\n    name\n    description\n    seoDescription\n    seoTitle\n  }\n}\n\nfragment ProductVariantLocaleItem on ProductVariant {\n  translation(languageCode: $locale) {\n    name\n  }\n}": types.CategoryLocaleItemFragmentDoc,
    "fragment MenuItem on MenuItem {\n  id\n  name\n  level\n  category {\n    id\n    slug\n    name\n  }\n  collection {\n    id\n    name\n    slug\n  }\n  page {\n    id\n    title\n    slug\n  }\n  url\n}\n\nquery MenuGetBySlug($slug: String!, $channel: String!) {\n  menu(slug: $slug, channel: $channel) {\n    items {\n      ...MenuItem\n      children {\n        ...MenuItem\n      }\n    }\n  }\n}": types.MenuItemFragmentDoc,
    "query OrderByid($orderId: ID!, $locale: LanguageCodeEnum!) {\n  order(id: $orderId) {\n    ...OrderItem\n  }\n}": types.OrderByidDocument,
    "fragment OrderDetails on Order {\n  id\n  number\n  created\n  total {\n    gross {\n      amount\n      currency\n    }\n  }\n  lines {\n    variant {\n      id\n      name\n      product {\n        id\n        name\n        description\n        slug\n        thumbnail {\n          url\n          alt\n        }\n        category {\n          id\n          name\n        }\n      }\n      pricing {\n        price {\n          gross {\n            amount\n            currency\n          }\n        }\n      }\n    }\n    quantity\n  }\n  paymentStatus\n}": types.OrderDetailsFragmentDoc,
    "fragment OrderItem on Order {\n  id\n  checkoutId\n  status\n  billingAddress {\n    city\n    cityArea\n    companyName\n    countryArea\n    country {\n      code\n    }\n    id\n    lastName\n    phone\n    streetAddress1\n    streetAddress2\n  }\n  shippingAddress {\n    city\n    cityArea\n    companyName\n    countryArea\n    country {\n      code\n    }\n    firstName\n    id\n    lastName\n    phone\n    streetAddress1\n    streetAddress2\n  }\n  lines {\n    id\n    quantity\n    totalPrice {\n      gross {\n        amount\n        currency\n      }\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n    }\n    variant {\n      ...ProductVariantItem\n      product {\n        media: metafield(key: \"media\")\n        name\n        slug\n      }\n    }\n  }\n  total {\n    currency\n  }\n  totalCharged {\n    amount\n    currency\n  }\n}": types.OrderItemFragmentDoc,
    "query PageGetBySlug($slug: String!) {\n  page(slug: $slug) {\n    id\n    slug\n    title\n    seoTitle\n    seoDescription\n    content\n  }\n}": types.PageGetBySlugDocument,
    "mutation PasswordChange($oldPassword: String!, $newPassword: String!) {\n  passwordChange(oldPassword: $oldPassword, newPassword: $newPassword) {\n    user {\n      email\n    }\n    errors {\n      message\n      code\n      field\n    }\n  }\n}": types.PasswordChangeDocument,
    "query ProductAllCategoriesList($locale: LanguageCodeEnum!, $first: Int, $after: String) {\n  categories(first: $first, after: $after) {\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n    edges {\n      node {\n        products {\n          totalCount\n        }\n        parent {\n          id\n          name\n          slug\n        }\n        ...AllCategoryChildrenList\n      }\n    }\n  }\n}\n\nfragment AllCategoryChildrenList on Category {\n  ...CategoryWithTranslation\n}": types.ProductAllCategoriesListDocument,
    "query ProductCardList($first: Int, $channel: String!, $locale: LanguageCodeEnum!, $filter: ProductFilterInput) {\n  products(\n    first: $first\n    channel: $channel\n    filter: $filter\n    sortBy: {field: DATE, direction: DESC}\n  ) {\n    edges {\n      node {\n        ...ProductCardItem\n      }\n    }\n  }\n}\n\nfragment ProductCardItem on Product {\n  name\n  translation(languageCode: $locale) {\n    name\n  }\n  metadata {\n    key\n    value\n  }\n  id\n  slug\n  rating\n  pricing {\n    priceRange {\n      start {\n        gross {\n          amount\n          currency\n        }\n      }\n      stop {\n        gross {\n          amount\n          currency\n        }\n      }\n    }\n  }\n  category {\n    name\n    translation(languageCode: $locale) {\n      name\n    }\n  }\n  variants {\n    ...ProductVariantItem\n  }\n}": types.ProductCardListDocument,
    "query ProductCategories($locale: LanguageCodeEnum!, $first: Int) {\n  categories(first: $first, level: 0) {\n    edges {\n      node {\n        ...CategoryChildren\n      }\n    }\n  }\n}\n\nfragment CategoryBase on Category {\n  id\n  name\n  slug\n  description\n  media: metafield(key: \"media\")\n  sortNumber: metafield(key: \"sortNumber\")\n  metadata {\n    key\n    value\n  }\n}\n\nfragment CategoryWithTranslation on Category {\n  ...CategoryBase\n  ...CategoryLocaleItem\n}\n\nfragment CategoryChildren on Category {\n  ...CategoryWithTranslation\n  children(first: $first) {\n    edges {\n      node {\n        ...CategoryWithTranslation\n        children(first: $first) {\n          edges {\n            node {\n              ...CategoryWithTranslation\n            }\n          }\n        }\n      }\n    }\n  }\n}": types.ProductCategoriesDocument,
    "query ProductCategoriesList($channel: String!, $locale: LanguageCodeEnum!, $first: Int) {\n  categories(first: $first, level: 0) {\n    edges {\n      node {\n        products(channel: $channel) {\n          totalCount\n        }\n        ...CategoryChildrenList\n      }\n    }\n  }\n}\n\nfragment CategoryChildrenList on Category {\n  ...CategoryWithTranslation\n  children(first: $first) {\n    totalCount\n    edges {\n      node {\n        ...CategoryWithTranslation\n        products {\n          totalCount\n        }\n      }\n    }\n  }\n}": types.ProductCategoriesListDocument,
    "query GetProductCategorySeo($locale: LanguageCodeEnum!, $slug: String!, $filterMetadataKey: String!) {\n  seo: category(slug: $slug) {\n    seoTitle\n    seoDescription\n    seoKeywords: metafield(key: $filterMetadataKey)\n    media: metafield(key: \"media\")\n    translation(languageCode: $locale) {\n      seoTitle\n      seoDescription\n    }\n  }\n}": types.GetProductCategorySeoDocument,
    "query ProductDetails($slug: String!, $channel: String!, $locale: LanguageCodeEnum!, $filterKeywordKey: String!) {\n  product(slug: $slug, channel: $channel) {\n    id\n    name\n    slug\n    seoTitle\n    seoDescription\n    description\n    seoKeywords: metafield(key: $filterKeywordKey)\n    translation(languageCode: $locale) {\n      name\n      descriptionJson\n      seoTitle\n      seoDescription\n    }\n    rating\n    isAvailable\n    defaultVariant {\n      id\n    }\n    productType {\n      isShippingRequired\n    }\n    collections {\n      name\n      slug\n      id\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    metadata {\n      key\n      value\n    }\n    descriptionJson\n    mp4: metafield(key: \"cover_mp4\")\n    media: metafield(key: \"media\")\n    threeModel: metafield(key: \"threeModel\")\n    threeModeCustoms: metafield(key: \"threeModel_customs\")\n    category {\n      id\n      name\n      slug\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    attributes {\n      attribute {\n        name\n        translation(languageCode: $locale) {\n          name\n        }\n      }\n      values {\n        name\n        value\n        translation(languageCode: $locale) {\n          name\n        }\n        inputType\n        file {\n          contentType\n          url\n        }\n      }\n    }\n    variants {\n      ...ProductDetailVariantItem\n    }\n    pricing {\n      priceRange {\n        start {\n          gross {\n            amount\n            currency\n          }\n        }\n        stop {\n          gross {\n            amount\n            currency\n          }\n        }\n      }\n    }\n  }\n}\n\nfragment ProductDetailVariantItem on ProductVariant {\n  quantityAvailable\n  sku\n  media: metafield(key: \"media\")\n  weight {\n    unit\n    value\n  }\n  attributes {\n    attribute {\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    values {\n      inputType\n      file {\n        url\n        contentType\n      }\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n  }\n  pricing {\n    price {\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n      gross {\n        amount\n        currency\n      }\n    }\n  }\n  translation(languageCode: $locale) {\n    name\n  }\n  name\n  id\n}": types.ProductDetailsDocument,
    "query ProductList($first: Int = 9, $channel: String!, $locale: LanguageCodeEnum!) {\n  products(\n    first: $first\n    channel: $channel\n    sortBy: {direction: DESC, field: DATE}\n  ) {\n    edges {\n      node {\n        ...ProductListItem\n      }\n    }\n  }\n}": types.ProductListDocument,
    "query ProductListByCategory($first: Int = 100, $slug: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  category(slug: $slug) {\n    ...CategoryLocaleItem\n    name\n    description\n    seoDescription\n    seoTitle\n    products(\n      first: $first\n      channel: $channel\n      sortBy: {direction: DESC, field: DATE}\n    ) {\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}": types.ProductListByCategoryDocument,
    "query ProductListByCategoryId($categoryId: ID!, $channel: String!, $locale: LanguageCodeEnum!) {\n  category(id: $categoryId) {\n    ...CategoryLocaleItem\n    name\n    description\n    seoDescription\n    seoTitle\n    products(first: 100, channel: $channel, sortBy: {direction: DESC, field: DATE}) {\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}": types.ProductListByCategoryIdDocument,
    "query ProductListByCategoryNew($slug: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  category(slug: $slug) {\n    ...CategoryLocaleItem\n    name\n    description\n    seoDescription\n    seoTitle\n    products(first: 4, channel: $channel, sortBy: {direction: DESC, field: DATE}) {\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}": types.ProductListByCategoryNewDocument,
    "query ProductListByCategoryPaginated($first: Int!, $after: String, $slug: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  category(slug: $slug) {\n    ...CategoryLocaleItem\n    name\n    description\n    seoDescription\n    seoTitle\n    products(\n      first: $first\n      after: $after\n      channel: $channel\n      sortBy: {direction: DESC, field: DATE}\n    ) {\n      totalCount\n      pageInfo {\n        endCursor\n        hasNextPage\n      }\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}": types.ProductListByCategoryPaginatedDocument,
    "query ProductListByCollection($slug: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  collection(slug: $slug, channel: $channel) {\n    name\n    description\n    seoDescription\n    seoTitle\n    products(first: 100, sortBy: {direction: DESC, field: DATE}) {\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}": types.ProductListByCollectionDocument,
    "query ProductListByIds($first: Int = 9, $channel: String!, $locale: LanguageCodeEnum!, $ids: [ID!]) {\n  products(\n    first: $first\n    channel: $channel\n    where: {ids: $ids}\n    sortBy: {direction: DESC, field: DATE}\n  ) {\n    totalCount\n    edges {\n      node {\n        attributes {\n          attribute {\n            name\n            translation(languageCode: $locale) {\n              name\n            }\n          }\n          values {\n            name\n            value\n            translation(languageCode: $locale) {\n              name\n            }\n            inputType\n            file {\n              contentType\n              url\n            }\n          }\n        }\n        variants {\n          ...ProductDetailVariantItem\n        }\n        ...ProductListItem\n      }\n    }\n  }\n}": types.ProductListByIdsDocument,
    "fragment ProductListItem on Product {\n  id\n  ...ProductLocaleItem\n  name\n  slug\n  rating\n  pricing {\n    priceRange {\n      start {\n        gross {\n          amount\n          currency\n        }\n      }\n      stop {\n        gross {\n          amount\n          currency\n        }\n      }\n    }\n  }\n  variants {\n    ...ProductDetailVariantItem\n  }\n  attributes {\n    attribute {\n      name\n      slug\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    values {\n      name\n      value\n      translation(languageCode: $locale) {\n        name\n      }\n      inputType\n      file {\n        contentType\n        url\n      }\n    }\n  }\n  descriptionJson\n  media: metafield(key: \"media\")\n  metadata {\n    key\n    value\n  }\n  translation(languageCode: $locale) {\n    descriptionJson\n    name\n  }\n  category {\n    id\n    ...CategoryLocaleItem\n    name\n    slug\n  }\n  thumbnail(size: 1024, format: WEBP) {\n    url\n    alt\n  }\n}\n\nfragment ProductDetailVariantItem on ProductVariant {\n  quantityAvailable\n  sku\n  media: metafield(key: \"media\")\n  weight {\n    unit\n    value\n  }\n  attributes {\n    attribute {\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    values {\n      inputType\n      file {\n        url\n        contentType\n      }\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n  }\n  pricing {\n    price {\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n      gross {\n        amount\n        currency\n      }\n    }\n  }\n  translation(languageCode: $locale) {\n    name\n  }\n  name\n  id\n}": types.ProductListItemFragmentDoc,
    "query ProductListPaginated($first: Int!, $after: String, $channel: String!, $locale: LanguageCodeEnum!) {\n  products(\n    first: $first\n    after: $after\n    channel: $channel\n    sortBy: {direction: DESC, field: DATE}\n  ) {\n    totalCount\n    edges {\n      node {\n        ...ProductListItem\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}": types.ProductListPaginatedDocument,
    "query GetProductSeo($channel: String!, $locale: LanguageCodeEnum!, $slug: String!, $filterMetadataKey: String!) {\n  seo: product(slug: $slug, channel: $channel) {\n    seoTitle\n    seoDescription\n    seoKeywords: metafield(key: $filterMetadataKey)\n    media: metafield(key: \"media\")\n    translation(languageCode: $locale) {\n      seoTitle\n      seoDescription\n    }\n  }\n}": types.GetProductSeoDocument,
    "query ProductSlugList($first: Int = 100, $channel: String!) {\n  products(first: $first, channel: $channel) {\n    edges {\n      node {\n        slug\n      }\n    }\n  }\n}": types.ProductSlugListDocument,
    "fragment ProductVariantItem on ProductVariant {\n  quantityAvailable\n  sku\n  metadata {\n    key\n    value\n  }\n  weight {\n    unit\n    value\n  }\n  attributes {\n    attribute {\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    values {\n      inputType\n      file {\n        url\n        contentType\n      }\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n  }\n  product {\n    id\n    name\n    translation(languageCode: $locale) {\n      name\n    }\n    metadata {\n      key\n      value\n    }\n    slug\n    category {\n      translation(languageCode: $locale) {\n        name\n      }\n      name\n    }\n  }\n  pricing {\n    price {\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n      gross {\n        amount\n        currency\n      }\n    }\n  }\n  translation(languageCode: $locale) {\n    name\n  }\n  name\n  id\n}": types.ProductVariantItemFragmentDoc,
    "query SearchProducts($search: String!, $sortBy: ProductOrderField!, $sortDirection: OrderDirection!, $first: Int!, $after: String, $channel: String!, $locale: LanguageCodeEnum!) {\n  products(\n    first: $first\n    after: $after\n    channel: $channel\n    sortBy: {field: $sortBy, direction: $sortDirection}\n    filter: {search: $search}\n  ) {\n    totalCount\n    edges {\n      node {\n        ...ProductListItem\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}": types.SearchProductsDocument,
    "mutation TokenVerify($token: String!) {\n  tokenVerify(token: $token) {\n    user {\n      email\n    }\n    errors {\n      field\n      code\n      message\n    }\n  }\n}": types.TokenVerifyDocument,
    "mutation UpdateCheckoutShippingMethod($checkoutId: ID!, $shippingMethodId: ID!) {\n  checkoutShippingMethodUpdate(\n    checkoutId: $checkoutId\n    shippingMethodId: $shippingMethodId\n  ) {\n    checkout {\n      id\n      shippingMethod {\n        id\n        name\n        price {\n          amount\n          currency\n        }\n      }\n    }\n    errors {\n      field\n      message\n    }\n  }\n}": types.UpdateCheckoutShippingMethodDocument,
    "mutation AccountAddressCreate($city: String, $companyName: String, $country: CountryCode, $countryArea: String, $firstName: String, $lastName: String, $phone: String, $streetAddress1: String, $postalCode: String) {\n  accountAddressCreate(\n    input: {city: $city, companyName: $companyName, country: $country, countryArea: $countryArea, firstName: $firstName, lastName: $lastName, phone: $phone, streetAddress1: $streetAddress1, postalCode: $postalCode}\n  ) {\n    address {\n      id\n      city\n      lastName\n      companyName\n      country {\n        country\n      }\n      firstName\n      countryArea\n      streetAddress2\n      streetAddress1\n      phone\n      countryArea\n      postalCode\n    }\n    errors {\n      field\n      message\n      code\n    }\n  }\n}": types.AccountAddressCreateDocument,
    "query MyQuery($id: ID!) {\n  user(id: $id) {\n    ...UserDetails\n  }\n}\n\nfragment UserDetails on User {\n  email\n  firstName\n  lastName\n  avatar {\n    alt\n    url\n  }\n}": types.MyQueryDocument,
    "mutation UserLogin($email: String!, $password: String!) {\n  tokenCreate(email: $email, password: $password) {\n    token\n    refreshToken\n    csrfToken\n    user {\n      email\n      id\n    }\n    errors {\n      code\n      field\n      message\n    }\n  }\n}": types.UserLoginDocument,
    "mutation UserRegister($email: String!, $password: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  accountRegister(\n    input: {email: $email, password: $password, channel: $channel, languageCode: $locale}\n  ) {\n    user {\n      email\n    }\n    errors {\n      field\n      message\n      code\n    }\n  }\n}": types.UserRegisterDocument,
    "mutation AccountAddressDelete($id: ID!) {\n  accountAddressDelete(id: $id) {\n    errors {\n      field\n      code\n      message\n    }\n    address {\n      id\n    }\n  }\n}": types.AccountAddressDeleteDocument,
    "mutation AddressSetDefault($addressId: ID!, $type: AddressTypeEnum!) {\n  accountSetDefaultAddress(id: $addressId, type: $type) {\n    user {\n      addresses {\n        firstName\n        phone\n        city\n        companyName\n        country {\n          country\n        }\n        lastName\n        streetAddress1\n      }\n    }\n    errors {\n      field\n      message\n      code\n    }\n  }\n}": types.AddressSetDefaultDocument,
    "mutation AccountAddressUpdate($id: ID!, $city: String, $companyName: String, $country: CountryCode, $countryArea: String, $firstName: String, $lastName: String, $phone: String, $streetAddress1: String, $postalCode: String) {\n  accountAddressUpdate(\n    id: $id\n    input: {city: $city, companyName: $companyName, country: $country, countryArea: $countryArea, firstName: $firstName, lastName: $lastName, phone: $phone, streetAddress1: $streetAddress1, postalCode: $postalCode}\n  ) {\n    errors {\n      field\n      code\n      message\n    }\n    address {\n      id\n      firstName\n      lastName\n      companyName\n      streetAddress1\n      city\n      countryArea\n      postalCode\n      country {\n        code\n        country\n      }\n      phone\n      isDefaultShippingAddress\n    }\n  }\n}": types.AccountAddressUpdateDocument,
    "query addressValidationRules($countryCode: CountryCode!) {\n  addressValidationRules(countryCode: $countryCode) {\n    countryAreaChoices {\n      raw\n      verbose\n      __typename\n    }\n    allowedFields\n    __typename\n  }\n}": types.AddressValidationRulesDocument,
    "mutation UpdateUserDetails($firstName: String!, $lastName: String!) {\n  accountUpdate(input: {firstName: $firstName, lastName: $lastName}) {\n    user {\n      id\n      email\n      firstName\n      lastName\n      avatar {\n        url\n        alt\n      }\n    }\n    errors {\n      field\n      message\n      code\n    }\n  }\n}": types.UpdateUserDetailsDocument,
    "fragment VariantDetails on ProductVariant {\n  id\n  name\n  ...ProductVariantLocaleItem\n  quantityAvailable\n  pricing {\n    price {\n      gross {\n        currency\n        amount\n      }\n    }\n  }\n}": types.VariantDetailsFragmentDoc,
    "mutation checkoutEmailUpdate($email: String!, $checkoutId: ID!) {\n  checkoutEmailUpdate(email: $email, checkoutId: $checkoutId) {\n    errors {\n      message\n      lines\n      code\n    }\n    checkout {\n      id\n    }\n  }\n}": types.CheckoutEmailUpdateDocument,
    "mutation checkoutBillingAddressUpdate($checkoutId: ID!, $firstName: String!, $lastName: String!, $country: CountryCode!, $companyName: String, $countryArea: String, $streetAddress1: String!, $city: String, $phone: String!, $postalCode: String!) {\n  checkoutBillingAddressUpdate(\n    checkoutId: $checkoutId\n    billingAddress: {firstName: $firstName, lastName: $lastName, country: $country, companyName: $companyName, countryArea: $countryArea, streetAddress1: $streetAddress1, phone: $phone, postalCode: $postalCode, city: $city}\n  ) {\n    errors {\n      code\n      field\n      message\n    }\n    checkout {\n      id\n      shippingAddress {\n        firstName\n        lastName\n        country {\n          code\n        }\n        companyName\n        countryArea\n        streetAddress1\n        city\n        phone\n        postalCode\n      }\n    }\n  }\n}": types.CheckoutBillingAddressUpdateDocument,
    "query Orderslist($first: Int, $before: String, $after: String) {\n  me {\n    orders(after: $after, before: $before, first: $first) {\n      totalCount\n      edges {\n        cursor\n        node {\n          id\n          status\n          created\n          totalCharged {\n            amount\n            currency\n          }\n          checkoutId\n          shippingAddress {\n            city\n            cityArea\n            companyName\n            countryArea\n            firstName\n            lastName\n            postalCode\n            country {\n              code\n              country\n            }\n            streetAddress1\n            isDefaultBillingAddress\n            isDefaultShippingAddress\n            id\n          }\n          lines {\n            id\n            quantity\n            totalPrice {\n              gross {\n                amount\n                currency\n              }\n              tax {\n                amount\n                currency\n              }\n              net {\n                amount\n                currency\n              }\n            }\n            variant {\n              quantityAvailable\n              sku\n              metadata {\n                key\n                value\n              }\n              weight {\n                unit\n                value\n              }\n              product {\n                id\n                media: metafield(key: \"media\")\n                name\n                slug\n              }\n            }\n          }\n          shippingPrice {\n            currency\n            gross {\n              amount\n              currency\n            }\n            net {\n              amount\n              currency\n            }\n            tax {\n              amount\n              currency\n            }\n          }\n        }\n      }\n      pageInfo {\n        endCursor\n        hasNextPage\n        hasPreviousPage\n        startCursor\n      }\n    }\n  }\n}": types.OrderslistDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ChannelsList {\n  channels {\n    id\n    name\n    slug\n    isActive\n    currencyCode\n    countries {\n      country\n      code\n    }\n  }\n}"): typeof import('./graphql').ChannelsListDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation CheckoutAddLine($id: ID!, $productVariantId: ID!, $quantity: Int!, $locale: LanguageCodeEnum!) {\n  checkoutLinesAdd(\n    id: $id\n    lines: [{quantity: $quantity, variantId: $productVariantId}]\n  ) {\n    checkout {\n      ...CheckoutItem\n    }\n    errors {\n      message\n    }\n  }\n}"): typeof import('./graphql').CheckoutAddLineDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation checkoutAddPromoCode($checkoutId: ID!, $promoCode: String!) {\n  checkoutAddPromoCode(checkoutId: $checkoutId, promoCode: $promoCode) {\n    errors {\n      code\n      message\n    }\n    checkout {\n      id\n      giftCards {\n        displayCode\n        id\n        last4CodeChars\n      }\n    }\n  }\n}"): typeof import('./graphql').CheckoutAddPromoCodeDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation CheckoutCreate($channel: String!, $locale: LanguageCodeEnum!) {\n  checkoutCreate(input: {channel: $channel, lines: []}) {\n    checkout {\n      ...CheckoutItem\n    }\n  }\n}"): typeof import('./graphql').CheckoutCreateDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation CreateTransaction($checkoutId: ID!, $amount: PositiveDecimal!, $metadata: [MetadataInput!]) {\n  transactionCreate(\n    id: $checkoutId\n    transaction: {name: \"Payment transaction\", message: \"Checkout payment\", pspReference: \"paypal\", availableActions: [CHARGE], amountAuthorized: {amount: $amount, currency: \"USD\"}, amountCharged: {amount: 0, currency: \"USD\"}, amountRefunded: {amount: 0, currency: \"USD\"}, amountCanceled: {amount: 0, currency: \"USD\"}, metadata: $metadata}\n  ) {\n    transaction {\n      id\n      name\n      message\n      metadata {\n        key\n        value\n      }\n    }\n    errors {\n      field\n      message\n    }\n  }\n}"): typeof import('./graphql').CreateTransactionDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation checkoutRemovePromoCode($checkoutId: ID!, $promoCodeId: ID!) {\n  checkoutRemovePromoCode(checkoutId: $checkoutId, promoCodeId: $promoCodeId) {\n    errors {\n      code\n      message\n    }\n    checkout {\n      id\n      giftCards {\n        displayCode\n        id\n        last4CodeChars\n      }\n    }\n  }\n}"): typeof import('./graphql').CheckoutRemovePromoCodeDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation CheckoutLinesDelete($id: ID!, $linesIds: [ID!]!, $locale: LanguageCodeEnum!) {\n  checkoutLinesDelete(id: $id, linesIds: $linesIds) {\n    checkout {\n      ...CheckoutItem\n    }\n    errors {\n      message\n    }\n  }\n}"): typeof import('./graphql').CheckoutLinesDeleteDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation CheckoutDeliveryMethodUpdate($deliveryMethodId: ID!, $checkoutId: ID!) {\n  checkoutDeliveryMethodUpdate(\n    deliveryMethodId: $deliveryMethodId\n    id: $checkoutId\n  ) {\n    checkout {\n      id\n    }\n    errors {\n      code\n      field\n      message\n    }\n  }\n}"): typeof import('./graphql').CheckoutDeliveryMethodUpdateDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query CheckoutFind($id: ID!, $locale: LanguageCodeEnum!) {\n  checkout(id: $id) {\n    ...CheckoutItem\n  }\n}"): typeof import('./graphql').CheckoutFindDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment CheckoutItem on Checkout {\n  id\n  email\n  discount {\n    amount\n    currency\n  }\n  giftCards {\n    displayCode\n    id\n    last4CodeChars\n  }\n  billingAddress {\n    city\n    cityArea\n    companyName\n    countryArea\n    country {\n      code\n    }\n    id\n    lastName\n    phone\n    streetAddress1\n    streetAddress2\n  }\n  shippingAddress {\n    city\n    cityArea\n    companyName\n    countryArea\n    country {\n      code\n    }\n    firstName\n    id\n    lastName\n    phone\n    streetAddress1\n    streetAddress2\n  }\n  shippingMethod {\n    id\n    name\n    message\n  }\n  availableShippingMethods {\n    id\n  }\n  shippingMethods {\n    id\n  }\n  availableCollectionPoints {\n    id\n  }\n  lines {\n    id\n    quantity\n    totalPrice {\n      gross {\n        amount\n        currency\n      }\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n    }\n    variant {\n      ...ProductVariantItem\n      product {\n        media: metafield(key: \"media\")\n        name\n        slug\n        chargeTaxes\n      }\n    }\n  }\n  totalPrice {\n    gross {\n      amount\n      currency\n    }\n    net {\n      amount\n      currency\n    }\n    tax {\n      amount\n      currency\n    }\n  }\n}"): typeof import('./graphql').CheckoutItemFragmentDoc;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation CheckoutLinesUpdate($id: ID!, $locale: LanguageCodeEnum!, $lines: [CheckoutLineUpdateInput!]!) {\n  checkoutLinesUpdate(id: $id, lines: $lines) {\n    checkout {\n      ...CheckoutItem\n    }\n    errors {\n      code\n      message\n      field\n    }\n  }\n}"): typeof import('./graphql').CheckoutLinesUpdateDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation CheckoutShippingAddressUpdate($checkoutId: ID!, $firstName: String!, $lastName: String!, $country: CountryCode!, $companyName: String, $countryArea: String, $streetAddress1: String!, $city: String, $phone: String!, $postalCode: String!) {\n  checkoutShippingAddressUpdate(\n    checkoutId: $checkoutId\n    shippingAddress: {firstName: $firstName, lastName: $lastName, country: $country, companyName: $companyName, countryArea: $countryArea, streetAddress1: $streetAddress1, phone: $phone, postalCode: $postalCode, city: $city}\n  ) {\n    errors {\n      code\n      field\n      message\n    }\n    checkout {\n      id\n      shippingAddress {\n        firstName\n        lastName\n        country {\n          code\n        }\n        companyName\n        countryArea\n        streetAddress1\n        city\n        phone\n        postalCode\n      }\n    }\n  }\n}"): typeof import('./graphql').CheckoutShippingAddressUpdateDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query CollectionList($first: Int!, $channel: String!, $locale: LanguageCodeEnum!) {\n  collections(first: $first, channel: $channel) {\n    edges {\n      node {\n        id\n        name\n        slug\n        description\n        metadata {\n          key\n          value\n        }\n        translation(languageCode: $locale) {\n          name\n          description\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').CollectionListDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query CurrentUser {\n  me {\n    ...UserDetails\n  }\n}"): typeof import('./graphql').CurrentUserDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query CurrentUserOrderList {\n  me {\n    ...UserDetails\n    orders(first: 10) {\n      edges {\n        node {\n          ...OrderDetails\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').CurrentUserOrderListDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query FindCheckoutId($channel: String!) {\n  me {\n    checkouts(first: 1, last: 1, channel: $channel) {\n      edges {\n        node {\n          id\n          chargeStatus\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').FindCheckoutIdDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query GetShippingMethods($checkoutId: ID!) {\n  checkout(id: $checkoutId) {\n    availableShippingMethods {\n      id\n      name\n      price {\n        amount\n        currency\n      }\n    }\n  }\n}"): typeof import('./graphql').GetShippingMethodsDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query GetUserAddresses {\n  me {\n    addresses {\n      id\n      city\n      lastName\n      companyName\n      country {\n        country\n        code\n        vat {\n          countryCode\n        }\n      }\n      firstName\n      countryArea\n      streetAddress1\n      phone\n      postalCode\n      isDefaultShippingAddress\n      isDefaultBillingAddress\n    }\n  }\n}"): typeof import('./graphql').GetUserAddressesDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment CategoryLocaleItem on Category {\n  translation(languageCode: $locale) {\n    name\n    description\n    seoDescription\n    seoTitle\n  }\n}\n\nfragment ProductLocaleItem on Product {\n  translation(languageCode: $locale) {\n    name\n  }\n}\n\nfragment CollectionLocaleItem on Collection {\n  translation(languageCode: $locale) {\n    name\n    description\n    seoDescription\n    seoTitle\n  }\n}\n\nfragment ProductVariantLocaleItem on ProductVariant {\n  translation(languageCode: $locale) {\n    name\n  }\n}"): typeof import('./graphql').CategoryLocaleItemFragmentDoc;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment MenuItem on MenuItem {\n  id\n  name\n  level\n  category {\n    id\n    slug\n    name\n  }\n  collection {\n    id\n    name\n    slug\n  }\n  page {\n    id\n    title\n    slug\n  }\n  url\n}\n\nquery MenuGetBySlug($slug: String!, $channel: String!) {\n  menu(slug: $slug, channel: $channel) {\n    items {\n      ...MenuItem\n      children {\n        ...MenuItem\n      }\n    }\n  }\n}"): typeof import('./graphql').MenuItemFragmentDoc;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query OrderByid($orderId: ID!, $locale: LanguageCodeEnum!) {\n  order(id: $orderId) {\n    ...OrderItem\n  }\n}"): typeof import('./graphql').OrderByidDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment OrderDetails on Order {\n  id\n  number\n  created\n  total {\n    gross {\n      amount\n      currency\n    }\n  }\n  lines {\n    variant {\n      id\n      name\n      product {\n        id\n        name\n        description\n        slug\n        thumbnail {\n          url\n          alt\n        }\n        category {\n          id\n          name\n        }\n      }\n      pricing {\n        price {\n          gross {\n            amount\n            currency\n          }\n        }\n      }\n    }\n    quantity\n  }\n  paymentStatus\n}"): typeof import('./graphql').OrderDetailsFragmentDoc;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment OrderItem on Order {\n  id\n  checkoutId\n  status\n  billingAddress {\n    city\n    cityArea\n    companyName\n    countryArea\n    country {\n      code\n    }\n    id\n    lastName\n    phone\n    streetAddress1\n    streetAddress2\n  }\n  shippingAddress {\n    city\n    cityArea\n    companyName\n    countryArea\n    country {\n      code\n    }\n    firstName\n    id\n    lastName\n    phone\n    streetAddress1\n    streetAddress2\n  }\n  lines {\n    id\n    quantity\n    totalPrice {\n      gross {\n        amount\n        currency\n      }\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n    }\n    variant {\n      ...ProductVariantItem\n      product {\n        media: metafield(key: \"media\")\n        name\n        slug\n      }\n    }\n  }\n  total {\n    currency\n  }\n  totalCharged {\n    amount\n    currency\n  }\n}"): typeof import('./graphql').OrderItemFragmentDoc;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query PageGetBySlug($slug: String!) {\n  page(slug: $slug) {\n    id\n    slug\n    title\n    seoTitle\n    seoDescription\n    content\n  }\n}"): typeof import('./graphql').PageGetBySlugDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation PasswordChange($oldPassword: String!, $newPassword: String!) {\n  passwordChange(oldPassword: $oldPassword, newPassword: $newPassword) {\n    user {\n      email\n    }\n    errors {\n      message\n      code\n      field\n    }\n  }\n}"): typeof import('./graphql').PasswordChangeDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductAllCategoriesList($locale: LanguageCodeEnum!, $first: Int, $after: String) {\n  categories(first: $first, after: $after) {\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n    edges {\n      node {\n        products {\n          totalCount\n        }\n        parent {\n          id\n          name\n          slug\n        }\n        ...AllCategoryChildrenList\n      }\n    }\n  }\n}\n\nfragment AllCategoryChildrenList on Category {\n  ...CategoryWithTranslation\n}"): typeof import('./graphql').ProductAllCategoriesListDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductCardList($first: Int, $channel: String!, $locale: LanguageCodeEnum!, $filter: ProductFilterInput) {\n  products(\n    first: $first\n    channel: $channel\n    filter: $filter\n    sortBy: {field: DATE, direction: DESC}\n  ) {\n    edges {\n      node {\n        ...ProductCardItem\n      }\n    }\n  }\n}\n\nfragment ProductCardItem on Product {\n  name\n  translation(languageCode: $locale) {\n    name\n  }\n  metadata {\n    key\n    value\n  }\n  id\n  slug\n  rating\n  pricing {\n    priceRange {\n      start {\n        gross {\n          amount\n          currency\n        }\n      }\n      stop {\n        gross {\n          amount\n          currency\n        }\n      }\n    }\n  }\n  category {\n    name\n    translation(languageCode: $locale) {\n      name\n    }\n  }\n  variants {\n    ...ProductVariantItem\n  }\n}"): typeof import('./graphql').ProductCardListDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductCategories($locale: LanguageCodeEnum!, $first: Int) {\n  categories(first: $first, level: 0) {\n    edges {\n      node {\n        ...CategoryChildren\n      }\n    }\n  }\n}\n\nfragment CategoryBase on Category {\n  id\n  name\n  slug\n  description\n  media: metafield(key: \"media\")\n  sortNumber: metafield(key: \"sortNumber\")\n  metadata {\n    key\n    value\n  }\n}\n\nfragment CategoryWithTranslation on Category {\n  ...CategoryBase\n  ...CategoryLocaleItem\n}\n\nfragment CategoryChildren on Category {\n  ...CategoryWithTranslation\n  children(first: $first) {\n    edges {\n      node {\n        ...CategoryWithTranslation\n        children(first: $first) {\n          edges {\n            node {\n              ...CategoryWithTranslation\n            }\n          }\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductCategoriesDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductCategoriesList($channel: String!, $locale: LanguageCodeEnum!, $first: Int) {\n  categories(first: $first, level: 0) {\n    edges {\n      node {\n        products(channel: $channel) {\n          totalCount\n        }\n        ...CategoryChildrenList\n      }\n    }\n  }\n}\n\nfragment CategoryChildrenList on Category {\n  ...CategoryWithTranslation\n  children(first: $first) {\n    totalCount\n    edges {\n      node {\n        ...CategoryWithTranslation\n        products {\n          totalCount\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductCategoriesListDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query GetProductCategorySeo($locale: LanguageCodeEnum!, $slug: String!, $filterMetadataKey: String!) {\n  seo: category(slug: $slug) {\n    seoTitle\n    seoDescription\n    seoKeywords: metafield(key: $filterMetadataKey)\n    media: metafield(key: \"media\")\n    translation(languageCode: $locale) {\n      seoTitle\n      seoDescription\n    }\n  }\n}"): typeof import('./graphql').GetProductCategorySeoDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductDetails($slug: String!, $channel: String!, $locale: LanguageCodeEnum!, $filterKeywordKey: String!) {\n  product(slug: $slug, channel: $channel) {\n    id\n    name\n    slug\n    seoTitle\n    seoDescription\n    description\n    seoKeywords: metafield(key: $filterKeywordKey)\n    translation(languageCode: $locale) {\n      name\n      descriptionJson\n      seoTitle\n      seoDescription\n    }\n    rating\n    isAvailable\n    defaultVariant {\n      id\n    }\n    productType {\n      isShippingRequired\n    }\n    collections {\n      name\n      slug\n      id\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    metadata {\n      key\n      value\n    }\n    descriptionJson\n    mp4: metafield(key: \"cover_mp4\")\n    media: metafield(key: \"media\")\n    threeModel: metafield(key: \"threeModel\")\n    threeModeCustoms: metafield(key: \"threeModel_customs\")\n    category {\n      id\n      name\n      slug\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    attributes {\n      attribute {\n        name\n        translation(languageCode: $locale) {\n          name\n        }\n      }\n      values {\n        name\n        value\n        translation(languageCode: $locale) {\n          name\n        }\n        inputType\n        file {\n          contentType\n          url\n        }\n      }\n    }\n    variants {\n      ...ProductDetailVariantItem\n    }\n    pricing {\n      priceRange {\n        start {\n          gross {\n            amount\n            currency\n          }\n        }\n        stop {\n          gross {\n            amount\n            currency\n          }\n        }\n      }\n    }\n  }\n}\n\nfragment ProductDetailVariantItem on ProductVariant {\n  quantityAvailable\n  sku\n  media: metafield(key: \"media\")\n  weight {\n    unit\n    value\n  }\n  attributes {\n    attribute {\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    values {\n      inputType\n      file {\n        url\n        contentType\n      }\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n  }\n  pricing {\n    price {\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n      gross {\n        amount\n        currency\n      }\n    }\n  }\n  translation(languageCode: $locale) {\n    name\n  }\n  name\n  id\n}"): typeof import('./graphql').ProductDetailsDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductList($first: Int = 9, $channel: String!, $locale: LanguageCodeEnum!) {\n  products(\n    first: $first\n    channel: $channel\n    sortBy: {direction: DESC, field: DATE}\n  ) {\n    edges {\n      node {\n        ...ProductListItem\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductListDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductListByCategory($first: Int = 100, $slug: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  category(slug: $slug) {\n    ...CategoryLocaleItem\n    name\n    description\n    seoDescription\n    seoTitle\n    products(\n      first: $first\n      channel: $channel\n      sortBy: {direction: DESC, field: DATE}\n    ) {\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductListByCategoryDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductListByCategoryId($categoryId: ID!, $channel: String!, $locale: LanguageCodeEnum!) {\n  category(id: $categoryId) {\n    ...CategoryLocaleItem\n    name\n    description\n    seoDescription\n    seoTitle\n    products(first: 100, channel: $channel, sortBy: {direction: DESC, field: DATE}) {\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductListByCategoryIdDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductListByCategoryNew($slug: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  category(slug: $slug) {\n    ...CategoryLocaleItem\n    name\n    description\n    seoDescription\n    seoTitle\n    products(first: 4, channel: $channel, sortBy: {direction: DESC, field: DATE}) {\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductListByCategoryNewDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductListByCategoryPaginated($first: Int!, $after: String, $slug: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  category(slug: $slug) {\n    ...CategoryLocaleItem\n    name\n    description\n    seoDescription\n    seoTitle\n    products(\n      first: $first\n      after: $after\n      channel: $channel\n      sortBy: {direction: DESC, field: DATE}\n    ) {\n      totalCount\n      pageInfo {\n        endCursor\n        hasNextPage\n      }\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductListByCategoryPaginatedDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductListByCollection($slug: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  collection(slug: $slug, channel: $channel) {\n    name\n    description\n    seoDescription\n    seoTitle\n    products(first: 100, sortBy: {direction: DESC, field: DATE}) {\n      edges {\n        node {\n          ...ProductListItem\n        }\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductListByCollectionDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductListByIds($first: Int = 9, $channel: String!, $locale: LanguageCodeEnum!, $ids: [ID!]) {\n  products(\n    first: $first\n    channel: $channel\n    where: {ids: $ids}\n    sortBy: {direction: DESC, field: DATE}\n  ) {\n    totalCount\n    edges {\n      node {\n        attributes {\n          attribute {\n            name\n            translation(languageCode: $locale) {\n              name\n            }\n          }\n          values {\n            name\n            value\n            translation(languageCode: $locale) {\n              name\n            }\n            inputType\n            file {\n              contentType\n              url\n            }\n          }\n        }\n        variants {\n          ...ProductDetailVariantItem\n        }\n        ...ProductListItem\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductListByIdsDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment ProductListItem on Product {\n  id\n  ...ProductLocaleItem\n  name\n  slug\n  rating\n  pricing {\n    priceRange {\n      start {\n        gross {\n          amount\n          currency\n        }\n      }\n      stop {\n        gross {\n          amount\n          currency\n        }\n      }\n    }\n  }\n  variants {\n    ...ProductDetailVariantItem\n  }\n  attributes {\n    attribute {\n      name\n      slug\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    values {\n      name\n      value\n      translation(languageCode: $locale) {\n        name\n      }\n      inputType\n      file {\n        contentType\n        url\n      }\n    }\n  }\n  descriptionJson\n  media: metafield(key: \"media\")\n  metadata {\n    key\n    value\n  }\n  translation(languageCode: $locale) {\n    descriptionJson\n    name\n  }\n  category {\n    id\n    ...CategoryLocaleItem\n    name\n    slug\n  }\n  thumbnail(size: 1024, format: WEBP) {\n    url\n    alt\n  }\n}\n\nfragment ProductDetailVariantItem on ProductVariant {\n  quantityAvailable\n  sku\n  media: metafield(key: \"media\")\n  weight {\n    unit\n    value\n  }\n  attributes {\n    attribute {\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    values {\n      inputType\n      file {\n        url\n        contentType\n      }\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n  }\n  pricing {\n    price {\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n      gross {\n        amount\n        currency\n      }\n    }\n  }\n  translation(languageCode: $locale) {\n    name\n  }\n  name\n  id\n}"): typeof import('./graphql').ProductListItemFragmentDoc;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductListPaginated($first: Int!, $after: String, $channel: String!, $locale: LanguageCodeEnum!) {\n  products(\n    first: $first\n    after: $after\n    channel: $channel\n    sortBy: {direction: DESC, field: DATE}\n  ) {\n    totalCount\n    edges {\n      node {\n        ...ProductListItem\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}"): typeof import('./graphql').ProductListPaginatedDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query GetProductSeo($channel: String!, $locale: LanguageCodeEnum!, $slug: String!, $filterMetadataKey: String!) {\n  seo: product(slug: $slug, channel: $channel) {\n    seoTitle\n    seoDescription\n    seoKeywords: metafield(key: $filterMetadataKey)\n    media: metafield(key: \"media\")\n    translation(languageCode: $locale) {\n      seoTitle\n      seoDescription\n    }\n  }\n}"): typeof import('./graphql').GetProductSeoDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query ProductSlugList($first: Int = 100, $channel: String!) {\n  products(first: $first, channel: $channel) {\n    edges {\n      node {\n        slug\n      }\n    }\n  }\n}"): typeof import('./graphql').ProductSlugListDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment ProductVariantItem on ProductVariant {\n  quantityAvailable\n  sku\n  metadata {\n    key\n    value\n  }\n  weight {\n    unit\n    value\n  }\n  attributes {\n    attribute {\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n    values {\n      inputType\n      file {\n        url\n        contentType\n      }\n      name\n      translation(languageCode: $locale) {\n        name\n      }\n    }\n  }\n  product {\n    id\n    name\n    translation(languageCode: $locale) {\n      name\n    }\n    metadata {\n      key\n      value\n    }\n    slug\n    category {\n      translation(languageCode: $locale) {\n        name\n      }\n      name\n    }\n  }\n  pricing {\n    price {\n      tax {\n        amount\n        currency\n      }\n      net {\n        amount\n        currency\n      }\n      gross {\n        amount\n        currency\n      }\n    }\n  }\n  translation(languageCode: $locale) {\n    name\n  }\n  name\n  id\n}"): typeof import('./graphql').ProductVariantItemFragmentDoc;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query SearchProducts($search: String!, $sortBy: ProductOrderField!, $sortDirection: OrderDirection!, $first: Int!, $after: String, $channel: String!, $locale: LanguageCodeEnum!) {\n  products(\n    first: $first\n    after: $after\n    channel: $channel\n    sortBy: {field: $sortBy, direction: $sortDirection}\n    filter: {search: $search}\n  ) {\n    totalCount\n    edges {\n      node {\n        ...ProductListItem\n      }\n      cursor\n    }\n    pageInfo {\n      endCursor\n      hasNextPage\n    }\n  }\n}"): typeof import('./graphql').SearchProductsDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation TokenVerify($token: String!) {\n  tokenVerify(token: $token) {\n    user {\n      email\n    }\n    errors {\n      field\n      code\n      message\n    }\n  }\n}"): typeof import('./graphql').TokenVerifyDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation UpdateCheckoutShippingMethod($checkoutId: ID!, $shippingMethodId: ID!) {\n  checkoutShippingMethodUpdate(\n    checkoutId: $checkoutId\n    shippingMethodId: $shippingMethodId\n  ) {\n    checkout {\n      id\n      shippingMethod {\n        id\n        name\n        price {\n          amount\n          currency\n        }\n      }\n    }\n    errors {\n      field\n      message\n    }\n  }\n}"): typeof import('./graphql').UpdateCheckoutShippingMethodDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation AccountAddressCreate($city: String, $companyName: String, $country: CountryCode, $countryArea: String, $firstName: String, $lastName: String, $phone: String, $streetAddress1: String, $postalCode: String) {\n  accountAddressCreate(\n    input: {city: $city, companyName: $companyName, country: $country, countryArea: $countryArea, firstName: $firstName, lastName: $lastName, phone: $phone, streetAddress1: $streetAddress1, postalCode: $postalCode}\n  ) {\n    address {\n      id\n      city\n      lastName\n      companyName\n      country {\n        country\n      }\n      firstName\n      countryArea\n      streetAddress2\n      streetAddress1\n      phone\n      countryArea\n      postalCode\n    }\n    errors {\n      field\n      message\n      code\n    }\n  }\n}"): typeof import('./graphql').AccountAddressCreateDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query MyQuery($id: ID!) {\n  user(id: $id) {\n    ...UserDetails\n  }\n}\n\nfragment UserDetails on User {\n  email\n  firstName\n  lastName\n  avatar {\n    alt\n    url\n  }\n}"): typeof import('./graphql').MyQueryDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation UserLogin($email: String!, $password: String!) {\n  tokenCreate(email: $email, password: $password) {\n    token\n    refreshToken\n    csrfToken\n    user {\n      email\n      id\n    }\n    errors {\n      code\n      field\n      message\n    }\n  }\n}"): typeof import('./graphql').UserLoginDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation UserRegister($email: String!, $password: String!, $channel: String!, $locale: LanguageCodeEnum!) {\n  accountRegister(\n    input: {email: $email, password: $password, channel: $channel, languageCode: $locale}\n  ) {\n    user {\n      email\n    }\n    errors {\n      field\n      message\n      code\n    }\n  }\n}"): typeof import('./graphql').UserRegisterDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation AccountAddressDelete($id: ID!) {\n  accountAddressDelete(id: $id) {\n    errors {\n      field\n      code\n      message\n    }\n    address {\n      id\n    }\n  }\n}"): typeof import('./graphql').AccountAddressDeleteDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation AddressSetDefault($addressId: ID!, $type: AddressTypeEnum!) {\n  accountSetDefaultAddress(id: $addressId, type: $type) {\n    user {\n      addresses {\n        firstName\n        phone\n        city\n        companyName\n        country {\n          country\n        }\n        lastName\n        streetAddress1\n      }\n    }\n    errors {\n      field\n      message\n      code\n    }\n  }\n}"): typeof import('./graphql').AddressSetDefaultDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation AccountAddressUpdate($id: ID!, $city: String, $companyName: String, $country: CountryCode, $countryArea: String, $firstName: String, $lastName: String, $phone: String, $streetAddress1: String, $postalCode: String) {\n  accountAddressUpdate(\n    id: $id\n    input: {city: $city, companyName: $companyName, country: $country, countryArea: $countryArea, firstName: $firstName, lastName: $lastName, phone: $phone, streetAddress1: $streetAddress1, postalCode: $postalCode}\n  ) {\n    errors {\n      field\n      code\n      message\n    }\n    address {\n      id\n      firstName\n      lastName\n      companyName\n      streetAddress1\n      city\n      countryArea\n      postalCode\n      country {\n        code\n        country\n      }\n      phone\n      isDefaultShippingAddress\n    }\n  }\n}"): typeof import('./graphql').AccountAddressUpdateDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query addressValidationRules($countryCode: CountryCode!) {\n  addressValidationRules(countryCode: $countryCode) {\n    countryAreaChoices {\n      raw\n      verbose\n      __typename\n    }\n    allowedFields\n    __typename\n  }\n}"): typeof import('./graphql').AddressValidationRulesDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation UpdateUserDetails($firstName: String!, $lastName: String!) {\n  accountUpdate(input: {firstName: $firstName, lastName: $lastName}) {\n    user {\n      id\n      email\n      firstName\n      lastName\n      avatar {\n        url\n        alt\n      }\n    }\n    errors {\n      field\n      message\n      code\n    }\n  }\n}"): typeof import('./graphql').UpdateUserDetailsDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "fragment VariantDetails on ProductVariant {\n  id\n  name\n  ...ProductVariantLocaleItem\n  quantityAvailable\n  pricing {\n    price {\n      gross {\n        currency\n        amount\n      }\n    }\n  }\n}"): typeof import('./graphql').VariantDetailsFragmentDoc;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation checkoutEmailUpdate($email: String!, $checkoutId: ID!) {\n  checkoutEmailUpdate(email: $email, checkoutId: $checkoutId) {\n    errors {\n      message\n      lines\n      code\n    }\n    checkout {\n      id\n    }\n  }\n}"): typeof import('./graphql').CheckoutEmailUpdateDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "mutation checkoutBillingAddressUpdate($checkoutId: ID!, $firstName: String!, $lastName: String!, $country: CountryCode!, $companyName: String, $countryArea: String, $streetAddress1: String!, $city: String, $phone: String!, $postalCode: String!) {\n  checkoutBillingAddressUpdate(\n    checkoutId: $checkoutId\n    billingAddress: {firstName: $firstName, lastName: $lastName, country: $country, companyName: $companyName, countryArea: $countryArea, streetAddress1: $streetAddress1, phone: $phone, postalCode: $postalCode, city: $city}\n  ) {\n    errors {\n      code\n      field\n      message\n    }\n    checkout {\n      id\n      shippingAddress {\n        firstName\n        lastName\n        country {\n          code\n        }\n        companyName\n        countryArea\n        streetAddress1\n        city\n        phone\n        postalCode\n      }\n    }\n  }\n}"): typeof import('./graphql').CheckoutBillingAddressUpdateDocument;
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "query Orderslist($first: Int, $before: String, $after: String) {\n  me {\n    orders(after: $after, before: $before, first: $first) {\n      totalCount\n      edges {\n        cursor\n        node {\n          id\n          status\n          created\n          totalCharged {\n            amount\n            currency\n          }\n          checkoutId\n          shippingAddress {\n            city\n            cityArea\n            companyName\n            countryArea\n            firstName\n            lastName\n            postalCode\n            country {\n              code\n              country\n            }\n            streetAddress1\n            isDefaultBillingAddress\n            isDefaultShippingAddress\n            id\n          }\n          lines {\n            id\n            quantity\n            totalPrice {\n              gross {\n                amount\n                currency\n              }\n              tax {\n                amount\n                currency\n              }\n              net {\n                amount\n                currency\n              }\n            }\n            variant {\n              quantityAvailable\n              sku\n              metadata {\n                key\n                value\n              }\n              weight {\n                unit\n                value\n              }\n              product {\n                id\n                media: metafield(key: \"media\")\n                name\n                slug\n              }\n            }\n          }\n          shippingPrice {\n            currency\n            gross {\n              amount\n              currency\n            }\n            net {\n              amount\n              currency\n            }\n            tax {\n              amount\n              currency\n            }\n          }\n        }\n      }\n      pageInfo {\n        endCursor\n        hasNextPage\n        hasPreviousPage\n        startCursor\n      }\n    }\n  }\n}"): typeof import('./graphql').OrderslistDocument;


export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}
